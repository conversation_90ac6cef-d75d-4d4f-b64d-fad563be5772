import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSubscriptionAndPackageTables1751443829172 implements MigrationInterface {
    name = 'AddSubscriptionAndPackageTables1751443829172'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_USER_PASSWORD_RESET_TOKEN"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE INDEX "IDX_USER_PASSWORD_RESET_TOKEN" ON "users" ("passwordResetToken") WHERE ("passwordResetToken" IS NOT NULL)`);
    }

}
