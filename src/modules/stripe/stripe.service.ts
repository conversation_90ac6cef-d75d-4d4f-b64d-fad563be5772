// stripe/stripe.service.ts
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import Strip<PERSON> from 'stripe';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class StripeService {
  private readonly stripe: Stripe;

  constructor(private configService: ConfigService) {
    const stripeKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!stripeKey) {
      throw new InternalServerErrorException(
        'STRIPE_SECRET_KEY is not configured',
      );
    }
    this.stripe = new Stripe(stripeKey, {
      apiVersion: '2025-05-28.basil',
    });
  }

  public getStripeInstance(): Stripe {
    return this.stripe;
  }

  async getPrices() {
    return this.stripe.prices.list({
      active: true,
    });
  }

  async createCheckoutSession({
    priceId,
    userId,
    packageId,
  }: {
    priceId: string;
    userId:string;
    packageId: string;
  }) {
    try {
      const successUrl = this.configService.get<string>('STRIPE_SUCCESS_URL');
      const cancelUrl = this.configService.get<string>('STRIPE_CANCEL_URL');

      const session = await this.stripe.checkout.sessions.create({
        mode: 'subscription',
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: {
          userId,
          internalPackageId: packageId,
        },
      });

      return session.url;
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to create checkout session: ${error.message}`,
      );
    }
  }

  constructWebhookEvent(payload: Buffer, signature: string): Stripe.Event {
    const webhookSecret = this.configService.get<string>(
      'STRIPE_WEBHOOK_SECRET',
    );
    if (!webhookSecret) {
      throw new InternalServerErrorException(
        'STRIPE_WEBHOOK_SECRET is not configured',
      );
    }

    try {
      return this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );
    } catch (err) {
      throw new InternalServerErrorException(
        `Webhook signature error: ${err.message}`,
      );
    }
  }
}
