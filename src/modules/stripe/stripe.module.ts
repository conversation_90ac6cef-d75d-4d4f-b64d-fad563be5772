// stripe/stripe.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PriceModule } from '../price/price.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { PackagesModule } from '../packages/packages.module';
import { StripeService } from './stripe.service';
import { StripeController } from './stripe.controller';

@Module({
  imports: [
    ConfigModule, 
    PriceModule, 
    PackagesModule,
    forwardRef(() => SubscriptionModule)
  ],
  controllers: [StripeController],
  providers: [StripeService],
  exports: [StripeService],
})
export class StripeModule {}
