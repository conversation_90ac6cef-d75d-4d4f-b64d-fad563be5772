// stripe/stripe.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PriceModule } from '../price/price.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { StripeService } from './stripe.service';

@Module({
  imports: [ConfigModule, PriceModule, forwardRef(() => SubscriptionModule)],
  providers: [StripeService],

  exports: [StripeService],
})
export class StripeModule {}
