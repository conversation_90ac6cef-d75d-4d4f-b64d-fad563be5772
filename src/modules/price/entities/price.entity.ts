// price.entity.ts
import {Package} from 'src/modules/packages/entities/package.entity';
import {
    Entity,
    Column,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    ManyToOne,
  } from 'typeorm';
  
  @Entity('prices')
  export class Price {
    @PrimaryGeneratedColumn('uuid')
    id: string;
  
    @Column({ unique: true })
    stripePriceId: string; // e.g. "price_1NzV5gFkXjTxxxxxx"
  
    @Column()
    currency: string; // e.g. "usd"
  
    @Column('integer')
    unitAmount: number; // in the smallest currency unit (e.g. cents)
  
    @Column({ nullable: true })
    nickname: string;
  
    @Column({ default: false })
    active: boolean;
  
    @Column({ nullable: true })
    type: 'one_time' | 'recurring';
  
    @Column({ nullable: true })
    interval: 'day' | 'week' | 'month' | 'year'; // only for recurring
  
    @Column({ nullable: true, type: 'int' })
    intervalCount: number; // only for recurring
  
    @Column({ nullable: true, type: 'int' })
    trialPeriodDays: number;
  
    @Column({ nullable: true })
    usageType: 'licensed' | 'metered'; // for recurring
  
    @ManyToOne(() => Package, (pkg) => pkg.prices, { onDelete: 'CASCADE' })
    package: Package;
  
    @CreateDateColumn()
    createdAt: Date;
  
    @UpdateDateColumn()
    updatedAt: Date;
  }
  