import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import Stripe from 'stripe';
import { Repository } from 'typeorm';
import { StripeService } from '../stripe/stripe.service';
import { Package } from './entities/package.entity';

@Injectable()
export class PackagesService {
  constructor(
    @InjectRepository(Package)
    private packageRepository: Repository<Package>,
    private readonly stripeService: StripeService,
  ) {}

  async findAll() {
    return this.packageRepository.find();
  }

  findOne(id: string) {
    return this.packageRepository.findOne({ where: { id } });
  }

  async handleStripeProduct(product: Stripe.Product, eventType: string) {
    const existing = await this.packageRepository.findOne({
      where: { stripeProductId: product.id },
    });

    if (eventType === 'product.deleted') {
      if (existing) {
        // Option 1: soft delete nếu bạn dùng soft-delete
        // await this.repo.softRemove(existing);

        // Option 2: update thông tin là "deleted"
        existing.name = `[DELETED] ${existing.name}`;
        existing.stripeProductId = `${existing.stripeProductId}_deleted`;
        await this.packageRepository.save(existing);
      }
      return;
    }

    const packageData = {
      name: product.name,
      description: product.description ?? '',
      image: product.images?.[0] ?? '',
      stripeProductId: product.id,
    };

    if (existing) {
      await this.packageRepository.update(existing.id, packageData);
    } else {
      const newPackage = this.packageRepository.create(packageData);
      await this.packageRepository.save(newPackage);
    }
  }

  async onSubscription(userId: string, packageId: string) {
    if (!userId) {
      throw new BadRequestException('User ID is required');
    }

    const packageEntity = await this.packageRepository.findOne({
      where: { id: packageId },
      relations: ['prices'],
    });

    if (!packageEntity) {
      throw new NotFoundException(`Package with ID ${packageId} not found`);
    }

    const priceId = packageEntity.prices?.[0]?.id;
    if (!priceId) {
      throw new NotFoundException(
        `No active price found for package with ID ${packageId}`,
      );
    }

    return this.stripeService.createCheckoutSession({
      priceId,
      userId,
      packageId,
    });
  }
}
