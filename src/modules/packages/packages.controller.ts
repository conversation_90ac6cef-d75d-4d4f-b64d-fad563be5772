import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { PackagesService } from './packages.service';
import { Roles } from '../auth/decorators/role.decorator';
import { EUserRole } from '../user/dto/create-user.dto';
import { RoleGuard } from '../auth/guards/role.guard';

@Controller('packages')
export class PackagesController {
  constructor(private readonly packagesService: PackagesService) {}

  @Get()
  findAll() {
    return this.packagesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.packagesService.findOne(id);
  }

  @Post('/subscription')
  @UseGuards(RoleGuard)
  @Roles(EUserRole.INDEPENDENT_TEACHER, EUserRole.SCHOOL_MANAGER)
  subscription(@Body('id') id: string, @ActiveUser() user) {
    const userId = user?.id;
    if (!userId) {
      throw new Error(`userId not found`);
    }
    return this.packagesService.onSubscription(userId, id);
  }
}
