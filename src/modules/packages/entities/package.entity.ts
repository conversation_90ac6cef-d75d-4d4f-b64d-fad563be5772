import { Column, Entity, OneToMany } from 'typeorm';
import BaseEntity from '../../../core/entities/base-entity';
import { Price } from 'src/modules/price/entities/price.entity';

@Entity('package')
export class Package extends BaseEntity {
  @Column()
  name: string;

  @Column()
  description: string;

  @Column()
  image: string;

  // Stripe Product ID
  @Column({ unique: true })
  stripeProductId: string;

  // Một Product có thể có nhiều Price
  @OneToMany(() => Price, (price) => price.package)
  prices: Price[];
}
