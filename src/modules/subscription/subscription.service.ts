// src/subscription/subscription.service.ts
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subscription } from './entities/subscription.entity';
import { Package } from '../packages/entities/package.entity';
import { StripeService } from '../stripe/stripe.service';
import Stripe from 'stripe';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectRepository(Subscription)
    private readonly repo: Repository<Subscription>,
    @InjectRepository(Package)
    private readonly packageRepo: Repository<Package>,
    private readonly stripeService: StripeService,
  ) {}

  private toDateTime(stripeTimestamp: number): Date {
    return new Date(stripeTimestamp * 1000);
  }

  async createOrUpdateFromStripe(subscription: Stripe.Subscription) {
    const userId = subscription.metadata?.userId;
    const firstItem = subscription.items.data[0];
    if (!userId) {
      this.logger.error(
        `Webhook for subscription ${subscription.id} is missing userId in metadata.`,
      );
      throw new BadRequestException(
        'Subscription metadata must contain userId.',
      );
    }

    const price = firstItem?.price;
    if (!price) {
      throw new BadRequestException(
        'Subscription must have at least one price.',
      );
    }

    // Get current period dates from the first subscription item (Stripe API change)
    const currentPeriodStart = firstItem?.current_period_start;
    const currentPeriodEnd = firstItem?.current_period_end;

    if (!currentPeriodStart || !currentPeriodEnd) {
      throw new BadRequestException(
        'Subscription item must have billing period information.',
      );
    }

    const subscriptionData: Partial<Subscription> = {
      user_id: userId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer as string,
      stripe_price_id: price.id,
      stripe_product_id: price.product as string,
      start_date: this.toDateTime(subscription.start_date),
      current_period_start: this.toDateTime(currentPeriodStart),
      current_period_end: this.toDateTime(currentPeriodEnd),
      cancel_at: subscription.cancel_at
        ? this.toDateTime(subscription.cancel_at)
        : undefined,
      canceled_at: subscription.canceled_at
        ? this.toDateTime(subscription.canceled_at)
        : undefined,
      ended_at: subscription.ended_at
        ? this.toDateTime(subscription.ended_at)
        : undefined,
      cancel_at_period_end: subscription.cancel_at_period_end,
      status: subscription.status,
      price_unit_amount: price.unit_amount ?? 0,
      currency: price.currency,
      metadata: subscription.metadata,
      latest_invoice_id:
        typeof subscription.latest_invoice === 'string'
          ? subscription.latest_invoice
          : subscription.latest_invoice?.id,
      raw_data: subscription,
    };

    try {
      const existing = await this.repo.findOne({
        where: { stripe_subscription_id: subscription.id },
      });

      if (existing) {
        Object.assign(existing, subscriptionData);
        return this.repo.save(existing);
      }
      const newSubscription = this.repo.create(subscriptionData);
      return this.repo.save(newSubscription);
    } catch (error) {
      this.logger.error(
        `Failed to save or update subscription ${subscription.id}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Could not save or update subscription.',
      );
    }
  }

  /**
   * Find a user's subscription with package details
   * @param userId - The user ID to find subscription for
   * @returns Subscription with package details
   */
  async findByUserId(userId: string) {
    this.logger.log(`Finding subscription for user ID: ${userId}`);

    try {
      const subscription = await this.repo.findOne({
        where: { user_id: userId },
      });

      if (!subscription) {
        throw new NotFoundException(
          `No subscription found for user ID: ${userId}`,
        );
      }

      // Find the package details using stripe_product_id
      const packageDetails = await this.packageRepo.findOne({
        where: { stripeProductId: subscription.stripe_product_id },
        relations: ['prices'],
      });

      return {
        ...subscription,
        package: packageDetails,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to find subscription for user ${userId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Could not retrieve user subscription.',
      );
    }
  }

  /**
   * Get user's invoice history from Stripe
   * @param userId - The user ID to get invoices for
   * @returns List of user's invoices from Stripe
   */
  async getUserInvoices(userId: string) {
    this.logger.log(`Getting invoices for user ID: ${userId}`);

    try {
      // First get the user's subscription to obtain stripe_customer_id
      const subscription = await this.repo.findOne({
        where: { user_id: userId },
      });

      if (!subscription) {
        throw new NotFoundException(
          `No subscription found for user ID: ${userId}`,
        );
      }

      if (!subscription.stripe_customer_id) {
        throw new BadRequestException(
          'No Stripe customer ID found for user subscription.',
        );
      }

      // Get Stripe instance from StripeService
      const stripe = this.stripeService.getStripeInstance();

      // Fetch invoices from Stripe
      const invoices = await stripe.invoices.list({
        customer: subscription.stripe_customer_id,
        limit: 100, // Limit to last 100 invoices
      });

      this.logger.log(
        `Retrieved ${invoices.data.length} invoices for user ${userId}`,
      );

      return invoices.data;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(
        `Failed to get invoices for user ${userId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Could not retrieve user invoices.',
      );
    }
  }
}
