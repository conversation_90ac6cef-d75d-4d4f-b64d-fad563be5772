# User Subscription & Invoice Endpoints Implementation Guide

This guide outlines the implementation plan for creating new endpoints that allow users to view their subscription status and invoice history.

## 1. Overview

We will introduce two new endpoints to provide users with access to their subscription and billing information. This enhances user experience by allowing them to self-manage their account details.

- `GET /subscription`: Fetches the current active subscription for the authenticated user.
- `GET /subscription/invoices`: Retrieves a list of invoices for the authenticated user.

These endpoints will be read-only and will leverage the existing Stripe integration.

## 2. File Structure & New Components

We will create a new module dedicated to user-facing subscription management to keep the concerns separated from the backend Stripe webhook handling.

- **Module**: `src/modules/user-subscription/`
- **Controller**: `src/modules/user-subscription/user-subscription.controller.ts`
- **Service**: `src/modules/user-subscription/user-subscription.service.ts`
- **Module File**: `src/modules/user-subscription/user-subscription.module.ts`
- **DTOs**:
    - `src/modules/user-subscription/dto/subscription-response.dto.ts`
    - `src/modules/user-subscription/dto/invoice-response.dto.ts`

## 3. Endpoint Implementation Details

### 3.1. `GET /subscription` - Get User's Subscription

This endpoint will return the user's current subscription details.

- **Controller (`user-subscription.controller.ts`):**
    - Create a `getSubscription` method decorated with `@Get()` and appropriate guards (`@UseGuards(AuthGuard)`).
    - Use the `@ActiveUser()` decorator to get the current user's ID.
    - Call the `user-subscription.service.ts` to fetch the data.

- **Service (`user-subscription.service.ts`):**
    - Create a `findUserActiveSubscription` method.
    - It will query the `subscriptions` table (using the `Subscription` entity from `src/modules/subscription/entities/subscription.entity.ts`).
    - It will find the subscription where `user_id` matches and the `status` is 'active'.
    - If not found, it should throw a `NotFoundException`.
    - It should return data mapped to `SubscriptionResponseDto`.

### 3.2. `GET /subscription/invoices` - Get User's Invoices

This endpoint will return a list of the user's invoices from Stripe.

- **Controller (`user-subscription.controller.ts`):**
    - Create a `getInvoices` method decorated with `@Get('/invoices')`.
    - Use the `@ActiveUser()` decorator to get the current user's ID.
    - Call the `user-subscription.service.ts`.

- **Service (`user-subscription.service.ts`):**
    - Create a `listUserInvoices` method.
    - First, it will call `findUserActiveSubscription` to get the user's subscription and, crucially, their `stripe_customer_id`.
    - It will then use the `StripeService` (`src/modules/stripe/stripe.service.ts`) to get the Stripe instance: `this.stripeService.getStripeInstance()`.
    - With the Stripe instance, it will call `stripe.invoices.list({ customer: stripe_customer_id })`.
    - The list of invoices from Stripe should be mapped to `InvoiceResponseDto[]` and returned.

## 4. Data Transfer Objects (DTOs)

### `SubscriptionResponseDto`
This DTO will define the shape of the subscription data returned to the client. It will be a subset of the `Subscription` entity.

- `status`
- `current_period_start`
- `current_period_end`
- `cancel_at_period_end`
- `package_name` (We can get this by linking the `stripe_product_id` to the `Package` entity).

### `InvoiceResponseDto`
This DTO will format the invoice data from Stripe.

- `id`
- `amount_paid`
- `invoice_pdf`
- `created`
- `status`

## 5. Module Integration

The new `UserSubscriptionModule` will need to:
- Import `TypeOrmModule.forFeature([Subscription, Package])` to access the necessary entities.
- Import the `StripeModule` to use the `StripeService`.
- Be added to the main `AppModule`.

## 6. Security & Guards

All endpoints in the `UserSubscriptionController` must be protected by the `AuthGuard` to ensure only authenticated users can access them. The `@ActiveUser()` decorator will provide the user context. 